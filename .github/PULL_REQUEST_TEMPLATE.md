## Infrastructure Change Checklist

### Pre-deployment Verification
- [ ] Terraform plan has been reviewed
- [ ] No unexpected resource recreations (check for "Plan to replace")
- [ ] IP address changes have been identified and documented
- [ ] DNS records will be updated if needed
- [ ] SSL certificates will be renewed if needed

### Droplet Protection
- [ ] Confirmed `prevent_destroy = true` is set on critical resources
- [ ] Verified `ignore_changes` includes `user_data` and `ssh_keys`
- [ ] No changes that would force droplet recreation

### Domain Changes
- [ ] Domain configuration changes are intentional
- [ ] DNS propagation time has been considered
- [ ] Application deployments won't be affected

### Post-deployment Actions
- [ ] Verify all services are running after apply
- [ ] Check application accessibility
- [ ] Update any hardcoded IP references if changed

### Terraform Cloud Workspace
- [ ] Correct workspace will be used (`morph-production` for production)
- [ ] Auto-apply is disabled for manual approval
- [ ] Plan has been reviewed in Terraform Cloud UI

---

**Description of Changes:**
<!-- Describe what infrastructure changes this PR makes -->

**Impact Assessment:**
<!-- Will this cause any downtime? IP changes? Service disruptions? -->

**Rollback Plan:**
<!-- How can these changes be reverted if needed? -->
