name: Deploy to Dokku

on:
  workflow_run:
    workflows: ["CI"]
    branches: [main]
    types:
      - completed
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    # Only run if the CI workflow completed successfully
    if: ${{ github.event.workflow_run.conclusion == 'success' }}

    steps:
    - name: Show deployment info
      run: |
        echo "🔗 Deployment triggered by CI workflow completion"
        echo "CI Run ID: ${{ github.event.workflow_run.id }}"
        echo "CI Conclusion: ${{ github.event.workflow_run.conclusion }}"
        echo "CI Head SHA: ${{ github.event.workflow_run.head_sha }}"
        echo "CI Head Branch: ${{ github.event.workflow_run.head_branch }}"

    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.workflow_run.head_sha }}
        fetch-depth: 0

    - name: Validate GitHub Secrets
      run: |
        echo "🔍 Validating GitHub Secrets..."

        # Check if DOKKU_SSH_PRIVATE_KEY is set
        if [ -z "${{ secrets.DOKKU_SSH_PRIVATE_KEY }}" ]; then
          echo "❌ DOKKU_SSH_PRIVATE_KEY secret is not set"
          echo "Please add your SSH private key to GitHub repository secrets"
          exit 1
        fi
        echo "✅ DOKKU_SSH_PRIVATE_KEY secret is present"

        # Check other required secrets
        MISSING_SECRETS=""

        if [ -z "${{ secrets.IMAP_URL }}" ]; then
          MISSING_SECRETS="$MISSING_SECRETS IMAP_URL"
        fi

        if [ -z "${{ secrets.IMAP_USER }}" ]; then
          MISSING_SECRETS="$MISSING_SECRETS IMAP_USER"
        fi

        if [ -z "${{ secrets.IMAP_PASS }}" ]; then
          MISSING_SECRETS="$MISSING_SECRETS IMAP_PASS"
        fi

        if [ -z "${{ secrets.DIGITALOCEAN_BUCKET_ACCESS_KEY }}" ]; then
          MISSING_SECRETS="$MISSING_SECRETS DIGITALOCEAN_BUCKET_ACCESS_KEY"
        fi

        if [ -z "${{ secrets.DIGITALOCEAN_BUCKET_SECRET_KEY }}" ]; then
          MISSING_SECRETS="$MISSING_SECRETS DIGITALOCEAN_BUCKET_SECRET_KEY"
        fi

        if [ -z "${{ secrets.UPTRACE_DSN }}" ]; then
          MISSING_SECRETS="$MISSING_SECRETS UPTRACE_DSN"
        fi

        if [ -n "$MISSING_SECRETS" ]; then
          echo "⚠️  Missing optional secrets:$MISSING_SECRETS"
          echo "These are not required for deployment but may be needed for app functionality"
        else
          echo "✅ All application secrets are present"
        fi

    - name: Validate GitHub Variables
      run: |
        echo "🔍 Validating GitHub Variables..."

        echo "ENABLE_LETSENCRYPT: ${{ vars.ENABLE_LETSENCRYPT || 'not set (will default to false)' }}"
        echo "LETSENCRYPT_EMAIL: ${{ vars.LETSENCRYPT_EMAIL || 'not set (will <NAME_EMAIL>)' }}"

        echo "✅ GitHub Variables validation complete"

    - name: Setup SSH
      run: |
        echo "🔧 Setting up SSH configuration..."
        mkdir -p ~/.ssh

        # Write SSH private key
        echo "${{ secrets.DOKKU_SSH_PRIVATE_KEY }}" > ~/.ssh/dokku_key
        chmod 600 ~/.ssh/dokku_key

        # Add known hosts
        ssh-keyscan -H overachieverlabs.com >> ~/.ssh/known_hosts

        # Add SSH config
        cat >> ~/.ssh/config << EOF
        Host dokku-admin overachieverlabs.com
          HostName overachieverlabs.com
          User root
          IdentityFile ~/.ssh/dokku_key
          IdentitiesOnly yes
          StrictHostKeyChecking yes
        EOF

        echo "✅ SSH configuration complete"

    - name: Validate SSH Key Format
      run: |
        echo "🔍 Validating SSH key format..."

        # Check if key file exists and has content
        if [ ! -s ~/.ssh/dokku_key ]; then
          echo "❌ SSH key file is empty or doesn't exist"
          exit 1
        fi

        # Validate SSH key format
        if ssh-keygen -l -f ~/.ssh/dokku_key; then
          echo "✅ SSH key format is valid"
        else
          echo "❌ SSH key format is invalid"
          echo "Please ensure your DOKKU_SSH_PRIVATE_KEY secret contains a valid SSH private key"
          echo "The key should start with '-----BEGIN OPENSSH PRIVATE KEY-----' or similar"
          exit 1
        fi

    - name: Debug SSH Setup
      run: |
        echo "🔍 SSH Setup Debug Information..."
        echo "SSH directory contents:"
        ls -la ~/.ssh/

        echo "SSH key fingerprint:"
        ssh-keygen -l -f ~/.ssh/dokku_key

        echo "SSH config:"
        cat ~/.ssh/config

        echo "Known hosts:"
        cat ~/.ssh/known_hosts | grep overachieverlabs.com || echo "No known hosts entry found"

    - name: Test SSH connection
      run: |
        echo "🔗 Testing SSH connection..."

        # Test SSH connection using dokku-admin alias
        if ssh -o ConnectTimeout=10 dokku-admin "echo 'SSH connection successful'"; then
          echo "✅ SSH connection via dokku-admin successful"
        else
          echo "❌ SSH connection via dokku-admin failed"
          echo "🔍 Attempting connection with verbose output for debugging..."
          ssh -vvv -o ConnectTimeout=10 dokku-admin "echo 'SSH connection test'" 2>&1 | head -30
          exit 1
        fi

        # Test SSH connection using overachieverlabs.com (for Git operations)
        if ssh -o ConnectTimeout=10 overachieverlabs.com "echo 'SSH connection for Git successful'"; then
          echo "✅ SSH connection via overachieverlabs.com successful"
        else
          echo "❌ SSH connection via overachieverlabs.com failed"
          echo "💡 Troubleshooting tips:"
          echo "1. Verify your SSH public key is <NAME_EMAIL>:~/.ssh/authorized_keys"
          echo "2. Ensure the DOKKU_SSH_PRIVATE_KEY secret matches the public key on the server"
          echo "3. Check that the SSH key format is correct (OpenSSH format recommended)"
          echo "4. Verify the server is accessible and SSH service is running"
          exit 1
        fi

    - name: Setup Dokku app (if needed)
      env:
        IMAP_URL: ${{ secrets.IMAP_URL }}
        IMAP_USER: ${{ secrets.IMAP_USER }}
        IMAP_PASS: ${{ secrets.IMAP_PASS }}
        DIGITALOCEAN_BUCKET_ACCESS_KEY: ${{ secrets.DIGITALOCEAN_BUCKET_ACCESS_KEY }}
        DIGITALOCEAN_BUCKET_SECRET_KEY: ${{ secrets.DIGITALOCEAN_BUCKET_SECRET_KEY }}
        UPTRACE_DSN: ${{ secrets.UPTRACE_DSN }}
        ENABLE_LETSENCRYPT: ${{ vars.ENABLE_LETSENCRYPT || 'false' }}
        LETSENCRYPT_EMAIL: ${{ vars.LETSENCRYPT_EMAIL || '<EMAIL>' }}
      run: |
        chmod +x ./scripts/setup-dokku-app.sh
        ./scripts/setup-dokku-app.sh

    - name: Deploy to Dokku
      run: |
        chmod +x ./scripts/deploy.sh
        ./scripts/deploy.sh

    - name: Verify deployment
      run: |
        echo "Waiting for deployment to stabilize..."
        sleep 30
        
        # Check if app is running
        if ! ssh dokku-admin "dokku ps:report postmanpat" | grep -q "Running:.*true"; then
          echo "❌ App is not running"
          exit 1
        fi
        
        # Health check
        echo "Performing health check..."
        for i in {1..10}; do
          if curl -f -s https://postmanpat.overachieverlabs.com > /dev/null; then
            echo "✅ Health check passed"
            break
          fi
          echo "Health check attempt $i failed, retrying in 10 seconds..."
          sleep 10
          if [ $i -eq 10 ]; then
            echo "❌ Health check failed after 10 attempts"
            echo "Restarting app to attempt recovery..."
            ssh dokku-admin "dokku ps:restart postmanpat"
            exit 1
          fi
        done
        
        echo "🚀 Deployment successful!"
