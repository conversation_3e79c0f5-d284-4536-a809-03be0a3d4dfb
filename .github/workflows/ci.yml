name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main, '**' ]

jobs:
  lint:
    name: <PERSON> Linting
    runs-on: ubuntu-latest

    steps:
    - name: Check out code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.22.5'

    - name: Run Go Linting
      uses: golangci/golangci-lint-action@v4
      with:
        version: latest
        skip-cache: true
        args: --out-format=github-actions --timeout=5m
  
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    steps:
    - name: Check out code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.22.5'

    - name: Set up Node
      uses: actions/setup-node@v4
      with:
        node-version: '>=20.15.0'

    - name: Run unit tests
      run: make test > test_output.txt

    - name: Generate test coverage report
      run: go tool cover -html=./cover.out -o ./cover.html

    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: ./cover.html

    - name: Upload test_output report
      uses: actions/upload-artifact@v4
      with:
        name: test_output
        path: ./test_output.txt

  build:
    runs-on: ubuntu-latest
    needs:
      - test
      - lint

    steps:
    - name: Check out the repository
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.22.5'

    - name: Build application
      run: |
        echo "Building application to verify compilation..."
        go build -o bin/postmanpat ./cmd/postmanpat
        echo "✅ Application builds successfully"

    - name: Verify Dockerfiles
      run: |
        echo "Verifying Dockerfile syntax..."
        docker build --dry-run -f Dockerfile.cron . || echo "❌ Dockerfile.cron has issues"
        docker build --dry-run -f Dockerfile.ws . || echo "❌ Dockerfile.ws has issues"
        echo "✅ Dockerfile verification complete"