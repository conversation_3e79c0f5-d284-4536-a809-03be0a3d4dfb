# Notes for happier terraforming with PostmanPat and Github spaces

## Initial installation

* Install tfenv to manage Terraform versions:
  ```bash
  # Install tfenv using Homebrew
  brew install tfenv

  # Install the latest stable version of Terraform
  tfenv install latest

  # Use the installed version
  tfenv use latest

  # Verify installation
  terraform version
  ```

  Using tfenv allows you to easily switch between different versions of Terraform, which is helpful when working with different projects that might require specific Terraform versions.
* Create a DigitalOcean API token with comprehensive access:
    1. Go to the DigitalOcean dashboard: `https://cloud.digitalocean.com/account/api/tokens`
    2. Click on "Generate New Token"
    3. Give your token a descriptive name (e.g., "Terraform Full Access")
    4. For token scope:
       - Select "Full Access" for complete access to all resources (selected for this use case)
       - Select "Read Only" for read-only access
       - For more granular control, select "Custom Scopes" (recommended for specific Terraform use cases):
         - For comprehensive infrastructure management, enable these scopes:
           - `account` - For account-level operations
           - `balance` - For billing information
           - `database` - For database management
           - `droplet` - For virtual machine management
           - `firewall` - For firewall configurations
           - `image` - For custom images
           - `kubernetes` - For Kubernetes clusters
           - `load_balancer` - For load balancer management
           - `registry` - For container registry access (create, read, update, delete)
           - `spaces` - For object storage
           - `vpc` - For virtual private cloud management
           - `volume` - For block storage
    5. Click "Generate Token" and copy the token immediately (it will only be shown once)
    6. The token format will look like: `dop_v1_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

* For using this token with different DigitalOcean services:
    1. For Terraform: Use as the `DO_TOKEN` variable
    2. For Container Registry access:
       ```bash
       docker login -u <your-digitalocean-username> -p <your-token> registry.digitalocean.com
       ```
    3. For DigitalOcean CLI (doctl):
       ```bash
       doctl auth init --access-token <your-token>
       ```
    4. Store this token securely as it provides broad access to your DigitalOcean resources

## Running terraform

This project uses Terraform and Terraform Cloud to manage infrastructure on DigitalOcean.

### Setup

* Remember to setup `gh-codespace-token` (currently called `DO_PAT`) in the [Codespaces Secrets](https://github.com/aaronromeo/postmanpat/settings/secrets/codespaces)
* Run `source terraform/pre.sh` to prepare your environment

#### Setting the Terraform Version

This project uses a specific version of Terraform. You can set it using tfenv:

```bash
# Check the required version in the terraform configuration files
grep -r "required_version" --include="*.tf" .

# Install and use the required version (example: 1.5.7)
tfenv install 1.5.7
tfenv use 1.5.7
```

Alternatively, you can create a `.terraform-version` file in the project root:

```bash
# Create a .terraform-version file
echo "1.5.7" > .terraform-version

# tfenv will automatically use this version when in the project directory
cd /path/to/project
terraform version  # Should show the version specified in .terraform-version
```

1. **Using a .tfvars file** (recommended for local development):
   - Create a file named `terraform.tfvars` in your environment directory with:
     ```
     DO_TOKEN = "dop_v1_XXXXXXXXXXXX"
     DIGITALOCEAN_CONTAINER_REGISTRY_TOKEN = "dop_v1_XXXXXXXXXXXX"
     ```
   - Run terraform commands without specifying the token:
     ```bash
     terraform plan
     terraform apply
     ```

1. Login into Terraform Cloud:
   ```bash
   terraform login
   ```

1. Initialize the Terraform configuration:
   ```bash
   terraform init
   ```

1. Plan the changes to see the changes:
   ```bash
   terraform plan
   ```

### Executing updates

After making changes to your infrastructure code:

1. Run `terraform plan` to see what changes will be applied
2. Review the plan carefully
3. Run `terraform apply` to execute the changes
4. The token will be used to authenticate all DigitalOcean API calls made by Terraform

### Verifying token access

To verify your token has the correct permissions for different resources:

```bash
# Initialize doctl with your token
doctl auth init --access-token dop_v1_XXXXXXXXXXXX

# Test various resource access
doctl account get                  # Test account access
doctl registry repository list     # Test container registry access
doctl droplet list                 # Test droplet access
doctl kubernetes cluster list      # Test Kubernetes access
doctl database list                # Test database access
doctl volume list                  # Test volume access
doctl spaces list                  # Test spaces access
```

If any command returns an error about permissions, your token may not have the necessary scopes for that resource type.

### Troubleshooting

#### Provider Error: "Failed to query available provider packages"

If you encounter an error like this during `terraform init`:

```
Error: Failed to query available provider packages

Could not retrieve the list of available versions for provider hashicorp/digitalocean: provider registry registry.terraform.io does not have a provider named registry.terraform.io/hashicorp/digitalocean
```

This happens because Terraform is trying to use both `digitalocean/digitalocean` and `hashicorp/digitalocean` providers. To fix this:

1. Make sure each module that uses DigitalOcean resources has a `provider.tf` file specifying ALL required provider sources:

```hcl
terraform {
  required_providers {
    digitalocean = {
      source = "digitalocean/digitalocean"
    }
    null = {
      source = "hashicorp/null"
    }
    # Include any other providers used in the module
  }
}
```

2. Make sure your root module (in environments/production or environments/testing) also declares all required providers:

```hcl
terraform {
  # ... other configuration ...

  required_providers {
    digitalocean = {
      source = "digitalocean/digitalocean"
      version = "~> 2.40.0"
    }
    null = {
      source = "hashicorp/null"
    }
  }
}
```

3. Explicitly pass ALL provider configurations from the root module to the child module:

```hcl
module "droplet" {
  source = "github.com/aaronromeo/morph//modules/droplet"

  providers = {
    digitalocean = digitalocean
    null = null
    # Map any other providers used in the module
  }

  # other variables...
}
```

3. Run `terraform init` again

### Setting Up Terraform Cloud Workspace Variables

Before running Terraform commands with Terraform Cloud, you need to set up the workspace variables in Terraform Cloud:

1. Navigate to your Terraform Cloud workspace: (eg: `https://app.terraform.io/app/aaronromeo/workspaces/postmanpat-production/variables`)

2. Add all the variables from `environments/production/terraform.tfvars.sample` as workspace variables:

   | Variable | Description | Category | Sensitive |
   |----------|-------------|----------|----------|
   | `DO_TOKEN` | DigitalOcean API token with full access | Environment | Yes |
   | `DOMAIN` | Your domain name | Terraform | No |
   | `SSH_FINGERPRINTS` | List of SSH key fingerprints | Terraform | No |
   | `SUBDOMAIN` | Subdomain for the application | Terraform | No |

3. For list variables like `SSH_FINGERPRINTS`, make sure to use HCL format and check the "HCL" checkbox in Terraform Cloud.

4. For sensitive information (passwords, tokens, keys), mark the variable as sensitive by checking the "Sensitive" checkbox.

### Running Terraform Commands

Always run Terraform commands from the specific environment directory:

```bash
# For production
cd /path/to/morph/environments/production
terraform init
terraform plan
terraform apply

# For testing
cd /path/to/morph/environments/testing
terraform init
terraform plan
terraform apply
```

When using Terraform Cloud, your state is stored remotely, and operations are executed in Terraform Cloud's environment.

## Using Dokku

This project sets up a Dokku instance on DigitalOcean for deploying applications.

### Setting Up Local Access to Dokku

1. Install the Dokku CLI:
   ```bash
   brew install dokku/repo/dokku
   ```

2. Set up SSH for passwordless access:
   ```bash
   # Generate a new SSH key for Dokku
   ssh-keygen -t rsa -b 4096 -f ~/.ssh/dokku_key -N ""

   # Add the following to your ~/.ssh/config file
   Host your-dokku-domain.com
     HostName your-dokku-domain.com
     User dokku
     IdentityFile ~/.ssh/dokku_key
     IdentitiesOnly yes

   Host dokku-admin
     HostName your-dokku-domain.com
     User root
     IdentityFile ~/.ssh/dokku_key
     IdentitiesOnly yes
   ```

3. Add your SSH key to the Dokku server (ask an admin to do this if you don't have root access):
   ```bash
   # Copy your public key
   cat ~/.ssh/dokku_key.pub

   # Have an admin add it to the server
   ssh <EMAIL> "cat >> /home/<USER>/.ssh/authorized_keys" # Paste the key
   ```

### Deploying Applications to Dokku

1. Create a new application:
   ```bash
   ssh dokku-admin "dokku apps:create your-app-name"
   ```

2. Set up your local Git repository:
   ```bash
   # In your application directory
   git remote <NAME_EMAIL>:your-app-name
   ```

3. Deploy your application:
   ```bash
   git push dokku main
   ```

4. Set up SSL with Let's Encrypt:
   ```bash
   ssh dokku-admin "dokku letsencrypt:enable your-app-name"
   ```

5. Access your application at:
   - http://your-app-name.your-dokku-domain.com
   - https://your-app-name.your-dokku-domain.com (after enabling Let's Encrypt)

### Common Dokku Commands

```bash
# List all applications
ssh dokku-admin "dokku apps:list"

# Check application status
ssh dokku-admin "dokku ps:report your-app-name"

# View application logs
ssh dokku-admin "dokku logs your-app-name -t"

# Set environment variables
ssh dokku-admin "dokku config:set your-app-name KEY=VALUE"

# List environment variables
ssh dokku-admin "dokku config your-app-name"

# Restart an application
ssh dokku-admin "dokku ps:restart your-app-name"

# Create a PostgreSQL database
ssh dokku-admin "dokku postgres:create your-db-name"

# Link a database to an application
ssh dokku-admin "dokku postgres:link your-db-name your-app-name"
```

For more information, see the [Dokku documentation](https://dokku.com/docs/).

## Domain Management

This project supports managing multiple domains with different purposes through a flexible configuration system.

### Domain Configuration

Domains are configured in your `terraform.tfvars` file using the `domains` variable. Each domain has a name, type, and optional target:

```hcl
domains = {
  "digby" = {
    name = "digbymarksit.com"
    type = "static"  # Simple HTML page, not managed by Dokku
  }
  "overachiever" = {
    name = "overachieverlabs.com"
    type = "dokku"   # Dokku installation for app deployments
  }
  "redirect_example" = {
    name = "example-redirect.com"
    type = "redirect"
    target = "*************"  # Custom IP address to point to
  }
}

# Optional: explicitly set the primary Dokku domain
dokku_domain = "overachieverlabs.com"
```

### Domain Types

1. **`dokku`** - Domains managed by Dokku for application deployments
   - Creates both root (@) and wildcard (*) A records pointing to the Dokku droplet
   - Supports subdomains for individual applications (e.g., `myapp.overachieverlabs.com`)
   - The first `dokku` domain becomes the primary Dokku hostname unless `dokku_domain` is explicitly set

2. **`static`** - Domains for static content (HTML pages, etc.)
   - Creates root (@) A record pointing to the droplet by default
   - Can specify a custom `target` IP address if needed
   - No wildcard record created (subdomains not automatically supported)

3. **`redirect`** - Domains that redirect or point to custom targets
   - Must specify a `target` IP address
   - Useful for pointing domains to different servers or services

### Adding New Domains

To add a new domain:

1. **Add the domain configuration** to your `terraform.tfvars`:
   ```hcl
   domains = {
     # ... existing domains ...
     "new_service" = {
       name = "newservice.com"
       type = "dokku"  # or "static" or "redirect"
       target = "optional-custom-ip"  # only needed for redirect type
     }
   }
   ```

2. **Apply the Terraform changes**:
   ```bash
   cd environments/production  # or environments/testing
   terraform plan
   terraform apply
   ```

3. **Configure DNS** (if the domain is managed elsewhere):
   - Point your domain's nameservers to DigitalOcean's nameservers, or
   - Create A records pointing to the droplet IP address shown in the Terraform output

### Domain Management Features

- **Automatic domain creation**: Domains are automatically created in DigitalOcean if they don't exist
- **Existing domain detection**: Set `check_domain_exists = true` to avoid conflicts with existing domains
- **Flexible DNS records**: Different record types based on domain purpose
- **Easy expansion**: Add new domains without affecting existing ones
- **Clear outputs**: Terraform outputs show configured domains and their types

### Terraform Outputs

After applying, you can see domain information:

```bash
terraform output configured_domains  # All domains and their types
terraform output dokku_domains      # Domains configured for Dokku
terraform output static_domains     # Domains configured for static content
terraform output droplet_ip         # IP address of the droplet
terraform output dokku_hostname     # Primary Dokku hostname
```

### Migration from Single Domain

If you're migrating from the old single `DOMAIN` variable:

1. **Replace** the `DOMAIN = "example.com"` line in your `terraform.tfvars`
2. **Add** the new `domains` configuration as shown above
3. **Run** `terraform plan` to see what changes will be made
4. **Apply** the changes with `terraform apply`

The new system is backward compatible and will handle the migration smoothly.

### Example Migration

**Before (old single domain):**
```hcl
DOMAIN = "digbymarksit.com"
```

**After (new multi-domain system):**
```hcl
domains = {
  "digby" = {
    name = "digbymarksit.com"
    type = "static"  # Since this was just a simple HTML page
  }
  "overachiever" = {
    name = "overachieverlabs.com"
    type = "dokku"   # This is where Dokku should actually run
  }
}

dokku_domain = "overachieverlabs.com"  # Explicitly set the Dokku domain
```

This resolves the original issue where `digbymarksit.com` was incorrectly configured for Dokku when it should have been a static site, and `overachieverlabs.com` was hardcoded but not properly managed.
