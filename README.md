# PostmanPat

PostmanPat is a Go-based email processing and archival system that connects to IMAP email servers to automatically manage email messages. It provides automated email archival, cleanup, and a web interface for monitoring mailbox operations.

## Features

- **IMAP Email Processing**: Connects to any IMAP server to list and process mailboxes
- **Automated Email Archival**: Exports emails to structured storage (local filesystem or DigitalOcean Spaces)
- **Configurable Email Lifecycle**: Set mailboxes as exportable, deletable, or both with custom lifespans
- **Web Interface**: Monitor mailbox status and configurations via web dashboard
- **Cloud Storage Integration**: Seamless integration with DigitalOcean Spaces (S3-compatible)
- **Observability**: Built-in OpenTelemetry tracing and logging with Uptrace integration
- **Containerized Deployment**: Docker-based deployment with automated updates via Watchtower

## Architecture

PostmanPat operates in three main modes:

### 1. Mailbox Discovery (`mailboxnames`)
- Connects to IMAP server and discovers all available mailboxes
- Creates or updates `workingfiles/mailboxlist.json` with mailbox configurations
- Allows setting export/delete policies and lifespans for each mailbox

### 2. Message Processing (`reapmessages`)
- Processes emails based on configurations in `mailboxlist.json`
- Exports emails to structured storage (local or DigitalOcean Spaces)
- Deletes emails based on configured lifecycle policies
- Supports dry-run mode for testing configurations

### 3. Web Dashboard (`webserver`)
- Provides web interface on port 3000 for monitoring
- Displays mailbox configurations and status
- Serves static assets and provides REST endpoints

## Installation & Setup

### Prerequisites

- Go 1.21.5 or later
- Node.js 20+ (for web assets)
- IMAP email account credentials
- DigitalOcean Spaces credentials (for cloud storage)

### Environment Variables

Create a `.env` file with the following variables:

```bash
# IMAP Configuration
IMAP_URL=imap.example.com:993
IMAP_USER=<EMAIL>
IMAP_PASS=your-password

# DigitalOcean Spaces (S3-compatible storage)
DIGITALOCEAN_BUCKET_ACCESS_KEY=your-access-key
DIGITALOCEAN_BUCKET_SECRET_KEY=your-secret-key

# Observability (optional)
UPTRACE_DSN=your-uptrace-dsn
```

### Local Development

```bash
# Install dependencies
go mod download
npm install

# Build the application
make build

# Build web assets
make build-npm

# Run tests
make test

# Start web server locally
make webserver
```

## Usage

### Command Line Interface

PostmanPat provides three main commands:

#### 1. List Mailboxes
```bash
# Discover and configure mailboxes
./build/postmanpat mailboxnames
# or using alias
./build/postmanpat mn
```

#### 2. Process Messages
```bash
# Process emails based on mailbox configuration
./build/postmanpat reapmessages
# or using alias
./build/postmanpat re
```

#### 3. Web Server
```bash
# Start web dashboard
./build/postmanpat webserver
# or using alias
./build/postmanpat ws
```

### Configuration

#### Mailbox Configuration (`workingfiles/mailboxlist.json`)

After running `mailboxnames`, edit the generated configuration file:

```json
{
  "mailboxes": [
    {
      "name": "INBOX",
      "exportable": true,
      "deletable": false,
      "lifespan_days": 30
    },
    {
      "name": "Sent",
      "exportable": true,
      "deletable": true,
      "lifespan_days": 90
    }
  ]
}
```

- **exportable**: Whether to export emails from this mailbox
- **deletable**: Whether to delete emails after processing
- **lifespan_days**: How many days to keep emails before deletion

## Testing

### Running Tests

```bash
# Run all tests with coverage
make test

# Run specific package tests
go test ./pkg/models/mailbox -v

# Run tests with race detection
go test -race ./...
```

### Current Test Coverage
- **Overall**: ~57% statement coverage
- **IMAP Manager**: 75.9% coverage
- **Mailbox Processing**: 62.5% coverage
- **CLI Commands**: 0% coverage (needs improvement)
- **HTTP Handlers**: 0% coverage (needs improvement)

## Deployment

### Quick Start

#### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd postmanpat
   ```

2. **Set up environment**
   ```bash
   cp .env.sample .env
   # Edit .env with your configuration
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose up
   ```

4. **Access the application**
   - Web interface: http://localhost:3000

#### Production Deployment

The application automatically deploys to `http://postmanpat.overachieverlabs.com` when code is pushed to the `main` branch.

**Setup Requirements:**
- Configure GitHub secrets and variables (see [GITHUB_CONFIGURATION.md](GITHUB_CONFIGURATION.md))
- Ensure SSH access to the Dokku server is properly configured

For detailed deployment information, see [DEPLOYMENT.md](DEPLOYMENT.md).

### CI/CD Pipeline

The project uses GitHub Actions for continuous integration and deployment:

- **CI Pipeline** (`.github/workflows/ci.yml`): Runs on all pushes and pull requests
  - Linting with golangci-lint
  - Unit tests with coverage reporting
  - Build verification
  - Dockerfile syntax validation

- **Deployment Pipeline** (`.github/workflows/deploy.yml`): Runs on pushes to `main` branch
  - Automated deployment to Dokku instance
  - Health checks and automatic rollback on failure
  - SSL certificate management via Let's Encrypt

## Using Dokku

This project deploys to a Dokku instance on DigitalOcean for application hosting.

### Setting Up Local Access to Dokku

1. Install the Dokku CLI:
   ```bash
   brew install dokku/repo/dokku
   ```

2. Set up SSH for passwordless access:
   ```bash
   # Generate a new SSH key for Dokku
   ssh-keygen -t rsa -b 4096 -f ~/.ssh/dokku_key -N ""

   # Add the following to your ~/.ssh/config file
   Host overachieverlabs.com
     HostName overachieverlabs.com
     User root
     IdentityFile ~/.ssh/dokku_key
     IdentitiesOnly yes

   Host dokku-admin
     HostName overachieverlabs.com
     User root
     IdentityFile ~/.ssh/dokku_key
     IdentitiesOnly yes
   ```

3. Add your SSH key to the Dokku server (ask an admin to do this if you don't have root access):
   ```bash
   # Copy your public key
   cat ~/.ssh/dokku_key.pub

   # Have an admin add it to the server
   ssh <EMAIL> "cat >> ~/.ssh/authorized_keys" # Paste the key
   ```

### Deploying Applications to Dokku

1. Create a new application:
   ```bash
   ssh dokku-admin "dokku apps:create your-app-name"
   ```

2. Set up your local Git repository:
   ```bash
   # In your application directory
   git remote <NAME_EMAIL>:your-app-name
   ```

3. Deploy your application:
   ```bash
   git push dokku main
   ```

4. Set up SSL with Let's Encrypt:
   ```bash
   ssh dokku-admin "dokku letsencrypt:enable your-app-name"
   ```

5. Access your application at:
   - http://your-app-name.your-dokku-domain.com
   - https://your-app-name.your-dokku-domain.com (after enabling Let's Encrypt)

### Common Dokku Commands

```bash
# List all applications
ssh dokku-admin "dokku apps:list"

# Check application status
ssh dokku-admin "dokku ps:report your-app-name"

# View application logs
ssh dokku-admin "dokku logs your-app-name -t"

# Set environment variables
ssh dokku-admin "dokku config:set your-app-name KEY=VALUE"

# List environment variables
ssh dokku-admin "dokku config your-app-name"

# Restart an application
ssh dokku-admin "dokku ps:restart your-app-name"

# Create a PostgreSQL database
ssh dokku-admin "dokku postgres:create your-db-name"

# Link a database to an application
ssh dokku-admin "dokku postgres:link your-db-name your-app-name"
```

For more information, see the [Dokku documentation](https://dokku.com/docs/).

## Domain Management

This project supports managing multiple domains with different purposes through a flexible configuration system.

### Domain Configuration

Domains are configured in your `terraform.tfvars` file using the `domains` variable. Each domain has a name, type, and optional target:

```hcl
domains = {
  "digby" = {
    name = "digbymarksit.com"
    type = "static"  # Simple HTML page, not managed by Dokku
  }
  "overachiever" = {
    name = "overachieverlabs.com"
    type = "dokku"   # Dokku installation for app deployments
  }
  "redirect_example" = {
    name = "example-redirect.com"
    type = "redirect"
    target = "*************"  # Custom IP address to point to
  }
}

# Optional: explicitly set the primary Dokku domain
dokku_domain = "overachieverlabs.com"
```

### Domain Types

1. **`dokku`** - Domains managed by Dokku for application deployments
   - Creates both root (@) and wildcard (*) A records pointing to the Dokku droplet
   - Supports subdomains for individual applications (e.g., `myapp.overachieverlabs.com`)
   - The first `dokku` domain becomes the primary Dokku hostname unless `dokku_domain` is explicitly set

2. **`static`** - Domains for static content (HTML pages, etc.)
   - Creates root (@) A record pointing to the droplet by default
   - Can specify a custom `target` IP address if needed
   - No wildcard record created (subdomains not automatically supported)

3. **`redirect`** - Domains that redirect or point to custom targets
   - Must specify a `target` IP address
   - Useful for pointing domains to different servers or services

### Adding New Domains

To add a new domain:

1. **Add the domain configuration** to your `terraform.tfvars`:
   ```hcl
   domains = {
     # ... existing domains ...
     "new_service" = {
       name = "newservice.com"
       type = "dokku"  # or "static" or "redirect"
       target = "optional-custom-ip"  # only needed for redirect type
     }
   }
   ```

2. **Apply the Terraform changes**:
   ```bash
   cd environments/production  # or environments/testing
   terraform plan
   terraform apply
   ```

3. **Configure DNS** (if the domain is managed elsewhere):
   - Point your domain's nameservers to DigitalOcean's nameservers, or
   - Create A records pointing to the droplet IP address shown in the Terraform output

### Domain Management Features

- **Automatic domain creation**: Domains are automatically created in DigitalOcean if they don't exist
- **Existing domain detection**: Set `check_domain_exists = true` to avoid conflicts with existing domains
- **Flexible DNS records**: Different record types based on domain purpose
- **Easy expansion**: Add new domains without affecting existing ones
- **Clear outputs**: Terraform outputs show configured domains and their types

### Terraform Outputs

After applying, you can see domain information:

```bash
terraform output configured_domains  # All domains and their types
terraform output dokku_domains      # Domains configured for Dokku
terraform output static_domains     # Domains configured for static content
terraform output droplet_ip         # IP address of the droplet
terraform output dokku_hostname     # Primary Dokku hostname
```

### Migration from Single Domain

If you're migrating from the old single `DOMAIN` variable:

1. **Replace** the `DOMAIN = "example.com"` line in your `terraform.tfvars`
2. **Add** the new `domains` configuration as shown above
3. **Run** `terraform plan` to see what changes will be made
4. **Apply** the changes with `terraform apply`

The new system is backward compatible and will handle the migration smoothly.

### Example Migration

**Before (old single domain):**
```hcl
DOMAIN = "digbymarksit.com"
```

**After (new multi-domain system):**
```hcl
domains = {
  "digby" = {
    name = "digbymarksit.com"
    type = "static"  # Since this was just a simple HTML page
  }
  "overachiever" = {
    name = "overachieverlabs.com"
    type = "dokku"   # This is where Dokku should actually run
  }
}

dokku_domain = "overachieverlabs.com"  # Explicitly set the Dokku domain
```

This resolves the original issue where `digbymarksit.com` was incorrectly configured for Dokku when it should have been a static site, and `overachieverlabs.com` was hardcoded but not properly managed.
