module "droplet" {
  source = "github.com/aaronromeo/morph//modules/dokku"

  providers = {
    digitalocean = digitalocean
  }

  do_api_token = var.do_api_token

  environment      = "production"
  droplet_size     = "s-1vcpu-1gb"
  region           = var.region
  SSH_FINGERPRINTS = var.SSH_FINGERPRINTS
  dokku_hostname   = local.dokku_hostname
  PUBLIC_KEY_SHA   = var.PUBLIC_KEY_SHA
  PUBLIC_KEY_FILE  = var.PUBLIC_KEY_FILE
}

# Local variables for domain management
locals {
  # Determine the Dokku hostname from either the explicit variable or the first dokku domain
  dokku_hostname = var.dokku_domain != "" ? var.dokku_domain : (
    length([for k, v in var.domains : v.name if v.type == "dokku"]) > 0 ?
    [for k, v in var.domains : v.name if v.type == "dokku"][0] :
    "localhost"
  )

  # Separate domains by type for easier management
  dokku_domains = { for k, v in var.domains : k => v if v.type == "dokku" }
  static_domains = { for k, v in var.domains : k => v if v.type == "static" }
  redirect_domains = { for k, v in var.domains : k => v if v.type == "redirect" }
}

# Domain management - check if domains exist first
data "digitalocean_domain" "existing_domains" {
  for_each = var.check_domain_exists ? var.domains : {}
  name     = each.value.name
}

# Create domains that don't exist or when not checking
resource "digitalocean_domain" "domains" {
  for_each = var.check_domain_exists ? {} : var.domains
  name     = each.value.name
}

# Get the actual domain names (either existing or newly created)
locals {
  domain_names = var.check_domain_exists ? {
    for k, v in var.domains : k => (
      contains(keys(data.digitalocean_domain.existing_domains), k) ?
      data.digitalocean_domain.existing_domains[k].name :
      v.name
    )
  } : {
    for k, v in digitalocean_domain.domains : k => v.name
  }
}

# DNS Records for Dokku domains
resource "digitalocean_record" "dokku_root_records" {
  for_each = local.dokku_domains

  domain = local.domain_names[each.key]
  type   = "A"
  name   = "@"
  ttl    = 1800
  value  = module.droplet.droplet_ip
}

resource "digitalocean_record" "dokku_wildcard_records" {
  for_each = local.dokku_domains

  domain = local.domain_names[each.key]
  type   = "A"
  name   = "*"
  ttl    = 1800
  value  = module.droplet.droplet_ip
}

# DNS Records for static domains (point to droplet but could be customized)
resource "digitalocean_record" "static_root_records" {
  for_each = local.static_domains

  domain = local.domain_names[each.key]
  type   = "A"
  name   = "@"
  ttl    = 1800
  value  = each.value.target != null ? each.value.target : module.droplet.droplet_ip
}

# DNS Records for redirect domains (could point to different targets)
resource "digitalocean_record" "redirect_root_records" {
  for_each = local.redirect_domains

  domain = local.domain_names[each.key]
  type   = "A"
  name   = "@"
  ttl    = 1800
  value  = each.value.target != null ? each.value.target : module.droplet.droplet_ip
}

# Outputs for domain information
output "droplet_ip" {
  description = "IP address of the Dokku droplet"
  value       = module.droplet.droplet_ip
}

output "dokku_hostname" {
  description = "Primary hostname configured for Dokku"
  value       = local.dokku_hostname
}

output "configured_domains" {
  description = "All configured domains and their types"
  value = {
    for k, v in var.domains : v.name => {
      type = v.type
      target = v.target
    }
  }
}

output "dokku_domains" {
  description = "Domains configured for Dokku"
  value       = [for k, v in local.dokku_domains : v.name]
}

output "static_domains" {
  description = "Domains configured for static content"
  value       = [for k, v in local.static_domains : v.name]
}