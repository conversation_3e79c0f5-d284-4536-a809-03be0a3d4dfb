
DIGITALOCEAN_BUCKET_ACCESS_KEY = "DO...."
DIGITALOCEAN_BUCKET_SECRET_KEY = "safsafsasadfas"
DIGITALOCEAN_CONTAINER_REGISTRY_TOKEN = "dop_...."
DIGITALOCEAN_USER = "<EMAIL>"
do_api_token = "dop_..."
# Domain configuration - define multiple domains with their purposes
domains = {
  "digby" = {
    name = "digbymarksit.com"
    type = "static"  # Simple HTML page, not managed by <PERSON>k<PERSON>
  }
  "overachiever" = {
    name = "overachieverlabs.com"
    type = "dokku"   # Dokku installation for app deployments
  }
}

# Optional: explicitly set the primary Dokku domain (otherwise uses first dokku domain)
dokku_domain = "overachieverlabs.com"
IMAP_PASS = "some-password"
IMAP_URL = "imap.gmail.com:993"
IMAP_USER = "user.name"
SSH_FINGERPRINTS = ["2a:44:16:e5:67:71:bb:06:0e:......."]
UPTRACE_DSN = "https://...."
