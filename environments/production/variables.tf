# Domain configuration
variable "domains" {
  description = "Map of domains and their configurations"
  type = map(object({
    name    = string
    type    = string # "dokku", "static", "redirect"
    target  = optional(string) # For redirects or custom targets
  }))
  default = {}
}

variable "dokku_domain" {
  description = "Primary domain for the Dokku instance"
  type        = string
  default     = ""
}

variable "pvt_key_file" {
  description = "Name of the SSH private key"
  type        = string
  default     = "do_tf"
}

variable "PVT_KEY" {
  description = "Path to the SSH private key file for connecting to the droplet"
  type        = string
  default     = "~/.ssh/do_tf"
}

# Reference to module variables
variable "do_api_token" {}
variable "PUBLIC_KEY_FILE" {
  description = "Path to the public key file"
  type        = string
  default     = "~/.ssh/do_tf.pub"
}
variable "SSH_FINGERPRINTS" {
  description = "List of SSH key fingerprints to add to the droplet"
  type        = list(string)
  default     = []
}
variable "region" {
  default = "nyc3"
}
variable "PUBLIC_KEY_SHA" {
  description = "SSH public key content (if provided, this will be used instead of reading from PUBLIC_KEY_FILE)"
  type        = string
  default     = ""
}

variable "check_domain_exists" {
  description = "Whether to check if the domain exists before creating it"
  type        = bool
  default     = false
}
